import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:farautorentify/models/vehicle_model.dart';
import 'package:farautorentify/providers/auth_provider.dart';
import 'package:farautorentify/screens/customer/booking_screen.dart';
import 'package:farautorentify/services/database_service.dart';
import 'package:farautorentify/utils/constants.dart';
import 'package:farautorentify/widgets/custom_button.dart';

class VehicleDetailScreen extends StatefulWidget {
  final VehicleModel vehicle;

  const VehicleDetailScreen({super.key, required this.vehicle});

  @override
  State<VehicleDetailScreen> createState() => _VehicleDetailScreenState();
}

class _VehicleDetailScreenState extends State<VehicleDetailScreen> {
  final DatabaseService _databaseService = DatabaseService();
  bool isInWishlist = false;
  bool isLoadingWishlist = false;

  @override
  void initState() {
    super.initState();
    _checkWishlistStatus();
  }

  void _checkWishlistStatus() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final userId = authProvider.currentUser?.uid;

    if (userId != null) {
      try {
        final inWishlist = await _databaseService.isInWishlist(
          userId,
          widget.vehicle.id,
        );
        if (mounted) {
          setState(() {
            isInWishlist = inWishlist;
          });
        }
      } catch (e) {
        print('DEBUG: Error checking wishlist status: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: CustomScrollView(
        slivers: [
          // App Bar with Image
          _buildSliverAppBar(),

          // Vehicle Details
          SliverToBoxAdapter(child: _buildVehicleDetails()),
        ],
      ),
      bottomNavigationBar: _buildBottomActionBar(),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 300,
      pinned: true,
      backgroundColor: AppConstants.primaryColor,
      foregroundColor: Colors.white,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(color: Colors.grey[300]),
          child: Stack(
            children: [
              const Center(
                child: Icon(
                  Icons.directions_car,
                  size: 100,
                  color: Colors.grey,
                ),
              ),
              Positioned(
                top: 50,
                right: 16,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.paddingSmall,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color:
                        widget.vehicle.isAvailable
                            ? AppConstants.successColor
                            : AppConstants.errorColor,
                    borderRadius: BorderRadius.circular(
                      AppConstants.borderRadiusSmall,
                    ),
                  ),
                  child: Text(
                    widget.vehicle.isAvailable ? 'Available' : 'Booked',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: AppConstants.fontSizeSmall,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: Icon(
            isInWishlist ? Icons.favorite : Icons.favorite_border,
            color: isInWishlist ? Colors.red : Colors.white,
          ),
          onPressed: _toggleWishlist,
        ),
      ],
    );
  }

  Widget _buildVehicleDetails() {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Basic Info
          _buildBasicInfo(),

          const Divider(),

          // Features
          _buildFeatures(),

          const Divider(),

          // Description
          _buildDescription(),

          const Divider(),

          // Specifications
          _buildSpecifications(),

          const SizedBox(height: 100), // Space for bottom bar
        ],
      ),
    );
  }

  Widget _buildBasicInfo() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.vehicle.name,
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeXXLarge,
                        fontWeight: FontWeight.bold,
                        color: AppConstants.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: AppConstants.paddingSmall),
                    Text(
                      '${widget.vehicle.brand} ${widget.vehicle.model}',
                      style: TextStyle(
                        fontSize: AppConstants.fontSizeLarge,
                        color: AppConstants.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '\$${widget.vehicle.pricePerDay.toStringAsFixed(0)}',
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeHeading,
                      fontWeight: FontWeight.bold,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                  Text(
                    'per day',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeMedium,
                      color: AppConstants.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          // Rating and Reviews (placeholder)
          Row(
            children: [
              Row(
                children: List.generate(5, (index) {
                  return Icon(
                    index < 4 ? Icons.star : Icons.star_border,
                    color: AppConstants.warningColor,
                    size: 20,
                  );
                }),
              ),
              const SizedBox(width: AppConstants.paddingSmall),
              Text(
                '4.0 (25 reviews)',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                  color: AppConstants.textSecondaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFeatures() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Features',
            style: TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Wrap(
            spacing: AppConstants.paddingSmall,
            runSpacing: AppConstants.paddingSmall,
            children:
                widget.vehicle.features.map((feature) {
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppConstants.paddingMedium,
                      vertical: AppConstants.paddingSmall,
                    ),
                    decoration: BoxDecoration(
                      color: AppConstants.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(
                        AppConstants.borderRadiusLarge,
                      ),
                      border: Border.all(
                        color: AppConstants.primaryColor.withOpacity(0.3),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.check_circle,
                          size: 16,
                          color: AppConstants.primaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          feature,
                          style: TextStyle(
                            fontSize: AppConstants.fontSizeMedium,
                            color: AppConstants.primaryColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildDescription() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Description',
            style: TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            widget.vehicle.description,
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: AppConstants.textSecondaryColor,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpecifications() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Specifications',
            style: TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          _buildSpecRow('Brand', widget.vehicle.brand),
          _buildSpecRow('Model', widget.vehicle.model),
          _buildSpecRow('Year', widget.vehicle.year.toString()),
          _buildSpecRow('Type', widget.vehicle.type.toUpperCase()),
          _buildSpecRow(
            'Status',
            widget.vehicle.isAvailable ? 'Available' : 'Not Available',
          ),
        ],
      ),
    );
  }

  Widget _buildSpecRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                color: AppConstants.textSecondaryColor,
              ),
            ),
          ),
          Text(
            ': $value',
            style: const TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.w500,
              color: AppConstants.textPrimaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActionBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: SecondaryButton(
                text: isInWishlist ? 'Remove from Wishlist' : 'Add to Wishlist',
                icon: isInWishlist ? Icons.favorite : Icons.favorite_border,
                onPressed: _toggleWishlist,
                isLoading: isLoadingWishlist,
              ),
            ),
            const SizedBox(width: AppConstants.paddingMedium),
            Expanded(
              child: PrimaryButton(
                text: 'Book Now',
                icon: Icons.calendar_today,
                onPressed:
                    widget.vehicle.isAvailable ? _navigateToBooking : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleWishlist() async {
    setState(() {
      isLoadingWishlist = true;
    });

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final userId = authProvider.currentUser?.uid;

    if (userId == null) {
      setState(() {
        isLoadingWishlist = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please login to add to wishlist'),
          backgroundColor: AppConstants.errorColor,
        ),
      );
      return;
    }

    try {
      if (isInWishlist) {
        await _databaseService.removeFromWishlist(userId, widget.vehicle.id);
        print('DEBUG: Removed ${widget.vehicle.name} from wishlist');
      } else {
        await _databaseService.addToWishlist(userId, widget.vehicle.id);
        print('DEBUG: Added ${widget.vehicle.name} to wishlist');
      }

      setState(() {
        isInWishlist = !isInWishlist;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isInWishlist
                  ? '${widget.vehicle.name} added to wishlist'
                  : '${widget.vehicle.name} removed from wishlist',
            ),
            backgroundColor: AppConstants.successColor,
          ),
        );
      }
    } catch (e) {
      print('DEBUG: Error toggling wishlist: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to update wishlist'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      setState(() {
        isLoadingWishlist = false;
      });
    }
  }

  void _navigateToBooking() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    if (!authProvider.isLoggedIn) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please login to book a vehicle'),
          backgroundColor: AppConstants.warningColor,
        ),
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BookingScreen(vehicle: widget.vehicle),
      ),
    );
  }
}
