import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:farautorentify/utils/constants.dart';

class HelpSupportScreen extends StatelessWidget {
  const HelpSupportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        title: const Text(
          'Help & Support',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            _buildHeaderSection(),

            const SizedBox(height: AppConstants.paddingLarge),

            // Contact Information
            _buildContactSection(context),

            const SizedBox(height: AppConstants.paddingLarge),

            // FAQ Section
            _buildFAQSection(),

            const SizedBox(height: AppConstants.paddingLarge),

            // Emergency Contact
            _buildEmergencySection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppConstants.primaryColor,
            AppConstants.primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
      ),
      child: Column(
        children: [
          Icon(Icons.support_agent, size: 60, color: Colors.white),
          const SizedBox(height: AppConstants.paddingMedium),
          const Text(
            'We\'re Here to Help!',
            style: TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Get in touch with our support team for any assistance',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: Colors.white.withOpacity(0.9),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildContactSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Contact Information',
            style: TextStyle(
              fontSize: AppConstants.fontSizeLarge,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          // Phone Number
          _buildContactItem(
            context,
            icon: Icons.phone,
            title: 'Customer Support Helpline',
            subtitle: '+92 300 1234567',
            onTap: () => _makePhoneCall('+923001234567'),
            actionIcon: Icons.call,
            actionColor: AppConstants.errorColor,
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          // Email
          _buildContactItem(
            context,
            icon: Icons.email,
            title: 'Email Support',
            subtitle: '<EMAIL>',
            onTap: () => _sendEmail('<EMAIL>'),
            actionIcon: Icons.email,
            actionColor: AppConstants.primaryColor,
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          // Business Hours
          _buildContactItem(
            context,
            icon: Icons.access_time,
            title: 'Business Hours',
            subtitle: 'Monday - Sunday: 9:00 AM - 10:00 PM',
            onTap: null,
            actionIcon: null,
            actionColor: null,
          ),
        ],
      ),
    );
  }

  Widget _buildContactItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback? onTap,
    required IconData? actionIcon,
    required Color? actionColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.backgroundColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppConstants.paddingSmall),
            decoration: BoxDecoration(
              color: AppConstants.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(
                AppConstants.borderRadiusSmall,
              ),
            ),
            child: Icon(icon, color: AppConstants.primaryColor, size: 24),
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: AppConstants.fontSizeMedium,
                    fontWeight: FontWeight.w600,
                    color: AppConstants.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeSmall,
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
          if (onTap != null && actionIcon != null) ...[
            const SizedBox(width: AppConstants.paddingSmall),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  onPressed: onTap,
                  icon: Icon(actionIcon, color: actionColor, size: 20),
                  style: IconButton.styleFrom(
                    backgroundColor: actionColor?.withOpacity(0.1),
                    padding: const EdgeInsets.all(8),
                  ),
                ),
                IconButton(
                  onPressed: () => _copyToClipboard(context, subtitle),
                  icon: const Icon(
                    Icons.copy,
                    color: AppConstants.textSecondaryColor,
                    size: 16,
                  ),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.grey.withOpacity(0.1),
                    padding: const EdgeInsets.all(8),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFAQSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Frequently Asked Questions',
            style: TextStyle(
              fontSize: AppConstants.fontSizeLarge,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          _buildFAQItem(
            'How do I book a vehicle?',
            'Browse our vehicle collection, select your preferred car or bike, choose your dates, and confirm your booking.',
          ),

          _buildFAQItem(
            'Can I cancel my booking?',
            'Yes, you can cancel pending or confirmed bookings from your booking history. Cancellation policies may apply.',
          ),

          _buildFAQItem(
            'What documents do I need?',
            'You need a valid driving license, CNIC, and the credit/debit card used for booking.',
          ),

          _buildFAQItem(
            'How do I contact support?',
            'You can call our helpline or send us an email. Our support team is available 9 AM to 10 PM daily.',
          ),
        ],
      ),
    );
  }

  Widget _buildFAQItem(String question, String answer) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            question,
            style: const TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.w600,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            answer,
            style: TextStyle(
              fontSize: AppConstants.fontSizeSmall,
              color: AppConstants.textSecondaryColor,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmergencySection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: AppConstants.errorColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        border: Border.all(color: AppConstants.errorColor.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.emergency, color: AppConstants.errorColor, size: 24),
              const SizedBox(width: AppConstants.paddingSmall),
              const Text(
                'Emergency Contact',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeLarge,
                  fontWeight: FontWeight.bold,
                  color: AppConstants.errorColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            'For urgent assistance or emergencies during your rental period:',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: AppConstants.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Row(
            children: [
              Icon(Icons.phone, color: AppConstants.errorColor, size: 20),
              const SizedBox(width: AppConstants.paddingSmall),
              Text(
                '+92 300 9876543 (24/7 Emergency)',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                  fontWeight: FontWeight.w600,
                  color: AppConstants.errorColor,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: () => _makePhoneCall('+923009876543'),
                icon: Icon(Icons.call, color: AppConstants.errorColor),
                style: IconButton.styleFrom(
                  backgroundColor: AppConstants.errorColor.withOpacity(0.1),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    }
  }

  Future<void> _sendEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=Support Request - FarAutoRentify',
    );
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    }
  }

  void _copyToClipboard(BuildContext context, String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$text copied to clipboard'),
        backgroundColor: AppConstants.successColor,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
